import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import Navigation from './Navigation';
import MobileNavigation from './MobileNavigation';
import HamburgerButton from './HamburgerButton';

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 导航项数据，后续可以从外部传入或在此定义
  const navItems = [
    { label: '首页', href: '/' },
    {
      label: '产品中心',
      href: '/products',
      children: [
        // { label: '路由器', href: '/products/routers' }, // 示例，实际链接待定
        // { label: 'IoT网关', href: '/products/iot-gateways' },
        { label: '查看全部', href: '/products' },
      ],
    },
    {
      label: '解决方案',
      href: '/solutions',
      children: [
        // { label: 'Wi-Fi 6', href: '/solutions/wifi-6' },
        { label: '网关-云端"物联网', href: '/solutions/gateway-to-cloud' },
        { label: 'GoodCloud 云平台', href: '/solutions/goodcloud' },
        {
          label: '为企业和工业物联网提供蜂窝网络连接',
          href: '/solutions/4g-iot-cellular-connectivity',
        },
        { label: '白标及客制化服务', href: '/solutions/white-label-service' },
        // { label: 'S2S 异地组网', href: '/solutions/s2s' },
      ],
    },
    { label: '联系我们', href: '/contact' },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="relative">
      <header className="bg-white border-b border-gray-200 py-4 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between">
          <div>
            <Link href="/">
              <Image
                src="/icons/logo.svg"
                alt="GL.iNet Logo"
                width={120}
                height={40}
              />
            </Link>
          </div>

          {/* 桌面端导航 */}
          <div className="hidden sm:hidden sm:hidden sm:hidden md:block">
            <Navigation navItems={navItems} />
          </div>

          {/* 移动端汉堡菜单按钮 */}
          <div className="block sm:block sm:block sm:block md:hidden">
            <HamburgerButton
              isOpen={isMobileMenuOpen}
              onClick={toggleMobileMenu}
            />
          </div>

          <div className="hidden sm:hidden sm:hidden sm:hidden md:block">{/* {{ 注释掉天猫按钮 }} */}</div>
        </div>
      </header>

      {/* 移动端导航菜单 */}
      <MobileNavigation
        navItems={navItems}
        isOpen={isMobileMenuOpen}
        onClose={closeMobileMenu}
      />
    </div>
  );
}
