import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function MobileNavigation({ navItems, isOpen, onClose }) {
  const [expandedItems, setExpandedItems] = useState(new Set());

  // 当菜单关闭时重置展开状态
  useEffect(() => {
    if (!isOpen) {
      setExpandedItems(new Set());
    }
  }, [isOpen]);

  // 切换子菜单展开状态
  const toggleExpanded = (label) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(label)) {
      newExpanded.delete(label);
    } else {
      newExpanded.add(label);
    }
    setExpandedItems(newExpanded);
  };

  // 处理菜单项点击
  const handleItemClick = (item) => {
    if (item.children && item.children.length > 0) {
      // 有子菜单的项目，切换展开状态
      toggleExpanded(item.label);
    } else {
      // 没有子菜单的项目，关闭菜单
      onClose();
    }
  };

  // 处理子菜单项点击
  const handleSubItemClick = () => {
    onClose();
  };

  // 处理背景点击关闭菜单
  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // 阻止滚动穿透
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* 背景遮罩 - 覆盖整个视口但不覆盖 Header */}
      <div
        className={`fixed inset-0 bg-black z-40 md:hidden transition-all duration-500 ease-in-out ${
          isOpen ? 'opacity-50' : 'opacity-0 pointer-events-none'
        }`}
        style={{ top: '100%' }}
        onClick={handleBackgroundClick}
      />

      {/* 导航菜单容器 - 绝对定位紧贴 Header 底部 */}
      <div
        className={`absolute left-0 right-0 top-full bg-white shadow-xl z-50 md:hidden transform transition-all duration-500 ease-in-out ${
          isOpen
            ? 'translate-y-0 opacity-100 scale-y-100'
            : '-translate-y-2 opacity-0 scale-y-95'
        }`}
        style={{
          transformOrigin: 'top center',
        }}
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-modal="true"
        aria-label="移动端导航菜单"
      >


        {/* 菜单内容 */}
        <nav className="py-0" role="navigation" aria-label="移动端主导航">
          <ul className="list-none">
            {navItems.map((item) => (
              <li key={item.label} className="border-b border-gray-100 last:border-b-0">
                {/* 主菜单项 */}
                <div
                  className="flex items-center justify-between px-4 py-4 cursor-pointer hover:bg-gray-50 hover:translate-x-1 active:scale-95 transition-all duration-200 ease-out"
                  onClick={() => handleItemClick(item)}
                  role="button"
                  tabIndex={0}
                  aria-expanded={item.children ? expandedItems.has(item.label) : undefined}
                  aria-haspopup={item.children ? 'true' : undefined}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleItemClick(item);
                    }
                  }}
                >
                  <Link
                    href={item.href}
                    className="flex-1 text-gray-800 font-medium text-base no-underline"
                    onClick={(e) => {
                      if (item.children && item.children.length > 0) {
                        e.preventDefault();
                      }
                    }}
                  >
                    {item.label}
                  </Link>
                  
                  {/* 展开箭头 */}
                  {item.children && item.children.length > 0 && (
                    <span
                      className={`ml-2 text-gray-500 transform transition-all duration-300 ease-in-out ${
                        expandedItems.has(item.label)
                          ? 'rotate-180 text-cyan-500 scale-110'
                          : 'rotate-0 scale-100'
                      }`}
                      aria-hidden="true"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </span>
                  )}
                </div>

                {/* 子菜单 */}
                {item.children && item.children.length > 0 && (
                  <div
                    className={`overflow-hidden transition-all duration-400 ease-in-out transform ${
                      expandedItems.has(item.label)
                        ? 'max-h-96 opacity-100 translate-y-0'
                        : 'max-h-0 opacity-0 -translate-y-2'
                    }`}
                    style={{
                      transformOrigin: 'top center',
                    }}
                  >
                    <ul className="bg-gray-50 list-none">
                      {item.children.map((subItem, index) => (
                        <li
                          key={subItem.label}
                          className={`transform transition-all duration-300 ease-out ${
                            expandedItems.has(item.label)
                              ? 'translate-x-0 opacity-100'
                              : 'translate-x-4 opacity-0'
                          }`}
                          style={{
                            transitionDelay: expandedItems.has(item.label) ? `${index * 50}ms` : '0ms'
                          }}
                        >
                          <Link
                            href={subItem.href}
                            className="block px-8 py-3 text-gray-700 text-sm no-underline hover:bg-gray-100 hover:text-cyan-500 hover:translate-x-1 transition-all duration-200"
                            onClick={handleSubItemClick}
                          >
                            {subItem.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </>
  );
}
